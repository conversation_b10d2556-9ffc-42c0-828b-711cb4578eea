[0.517s] Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[0.756s] running egg_info
[0.756s] writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
[0.757s] writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
[0.757s] writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
[0.757s] writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
[0.757s] writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
[0.760s] reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.761s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.761s] running build
[0.761s] running build_py
[0.761s] running install
[0.762s] running install_lib
[0.763s] running install_data
[0.763s] copying behavior_trees/follow_point.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.763s] copying behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.763s] copying behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.764s] running install_egg_info
[0.765s] removing '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info' (and everything under it)
[0.766s] Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
[0.766s] running install_scripts
[0.792s] Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
[0.792s] writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
[0.822s] Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
