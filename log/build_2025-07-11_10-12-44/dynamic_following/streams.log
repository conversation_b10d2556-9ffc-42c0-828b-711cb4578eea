[0.551s] Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[0.741s] running egg_info
[0.742s] writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
[0.742s] writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
[0.742s] writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
[0.743s] writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
[0.743s] writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
[0.744s] reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.745s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.745s] running build
[0.745s] running build_py
[0.745s] running install
[0.746s] running install_lib
[0.747s] running install_data
[0.747s] copying launch/test_minimal_nav2.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
[0.747s] running install_egg_info
[0.748s] removing '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info' (and everything under it)
[0.749s] Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
[0.749s] running install_scripts
[0.770s] Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
[0.770s] writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
[0.795s] Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
