running egg_info
writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
copying launch/test_minimal_nav2.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
running install_egg_info
removing '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info' (and everything under it)
Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
running install_scripts
Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
