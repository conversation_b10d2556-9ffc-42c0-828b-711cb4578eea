[0.112s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'dynamic_following']
[0.113s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['dynamic_following'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x72d694423ee0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x72d694423a90>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x72d694423a90>>)
[0.296s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.296s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.296s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.296s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.296s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.296s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.296s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/projects/dynamic_following'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.315s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_python' and name 'dynamic_following'
[0.316s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.316s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.316s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.316s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.316s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.340s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.340s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.343s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/projects/dynamic_following/install
[0.344s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.359s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'cmake_args' from command line to 'None'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'cmake_target' from command line to 'None'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'cmake_clean_cache' from command line to 'False'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'cmake_clean_first' from command line to 'False'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'cmake_force_configure' from command line to 'False'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'ament_cmake_args' from command line to 'None'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'catkin_cmake_args' from command line to 'None'
[0.404s] Level 5:colcon.colcon_core.verb:set package 'dynamic_following' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.404s] DEBUG:colcon.colcon_core.verb:Building package 'dynamic_following' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/projects/dynamic_following/build/dynamic_following', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/projects/dynamic_following/install/dynamic_following', 'merge_install': False, 'path': '/home/<USER>/projects/dynamic_following', 'symlink_install': False, 'test_result_base': None}
[0.404s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.406s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.406s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/projects/dynamic_following' with build type 'ament_python'
[0.406s] Level 1:colcon.colcon_core.shell:create_environment_hook('dynamic_following', 'ament_prefix_path')
[0.408s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.408s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/hook/ament_prefix_path.ps1'
[0.409s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/hook/ament_prefix_path.dsv'
[0.410s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/hook/ament_prefix_path.sh'
[0.410s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.410s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.629s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/projects/dynamic_following'
[0.629s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.629s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.982s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[1.241s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[1.242s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/dynamic_following/install/dynamic_following' for CMake module files
[1.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/dynamic_following/install/dynamic_following' for CMake config files
[1.244s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib'
[1.244s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/dynamic_following/install/dynamic_following/bin'
[1.244s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/pkgconfig/dynamic_following.pc'
[1.245s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages'
[1.245s] Level 1:colcon.colcon_core.shell:create_environment_hook('dynamic_following', 'pythonpath')
[1.245s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/hook/pythonpath.ps1'
[1.245s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/hook/pythonpath.dsv'
[1.246s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/hook/pythonpath.sh'
[1.246s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/dynamic_following/install/dynamic_following/bin'
[1.247s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(dynamic_following)
[1.247s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/package.ps1'
[1.248s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/package.dsv'
[1.249s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/package.sh'
[1.249s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/package.bash'
[1.250s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/package.zsh'
[1.251s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/projects/dynamic_following/install/dynamic_following/share/colcon-core/packages/dynamic_following)
[1.251s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.252s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.252s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.252s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.256s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.256s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.256s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.266s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.266s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/dynamic_following/install/local_setup.ps1'
[1.268s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/projects/dynamic_following/install/_local_setup_util_ps1.py'
[1.270s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/dynamic_following/install/setup.ps1'
[1.271s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/dynamic_following/install/local_setup.sh'
[1.272s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/projects/dynamic_following/install/_local_setup_util_sh.py'
[1.272s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/dynamic_following/install/setup.sh'
[1.273s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/dynamic_following/install/local_setup.bash'
[1.273s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/dynamic_following/install/setup.bash'
[1.274s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/dynamic_following/install/local_setup.zsh'
[1.274s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/dynamic_following/install/setup.zsh'
