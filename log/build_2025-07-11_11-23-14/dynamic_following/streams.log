[0.825s] Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[1.133s] running egg_info
[1.134s] writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
[1.135s] writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
[1.135s] writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
[1.135s] writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
[1.135s] writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
[1.139s] reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[1.141s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[1.141s] running build
[1.142s] running build_py
[1.142s] running install
[1.143s] running install_lib
[1.145s] running install_data
[1.146s] error: can't copy 'scripts/test_goal_updater.py': doesn't exist or not a regular file
[1.178s] Invoked command in '/home/<USER>/projects/dynamic_following' returned '1': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
