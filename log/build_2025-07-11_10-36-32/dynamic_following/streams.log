[0.429s] Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[0.607s] running egg_info
[0.608s] writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
[0.608s] writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
[0.608s] writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
[0.608s] writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
[0.608s] writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
[0.611s] reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.612s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.612s] running build
[0.612s] running build_py
[0.612s] running install
[0.613s] running install_lib
[0.614s] running install_data
[0.615s] copying params/nav2_params_dynamic_following.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.615s] running install_egg_info
[0.618s] removing '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info' (and everything under it)
[0.618s] Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
[0.619s] running install_scripts
[0.645s] Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
[0.646s] writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
[0.673s] Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
