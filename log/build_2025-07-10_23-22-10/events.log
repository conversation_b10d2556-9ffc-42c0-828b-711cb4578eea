[0.000000] (-) TimerEvent: {}
[0.000298] (nav2_test_utils) JobQueued: {'identifier': 'nav2_test_utils', 'dependencies': OrderedDict()}
[0.000531] (nav2_test_utils) JobStarted: {'identifier': 'nav2_test_utils'}
[0.006547] (nav2_test_utils) JobProgress: {'identifier': 'nav2_test_utils', 'progress': 'cmake'}
[0.006894] (nav2_test_utils) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/projects/nav2_test_utils', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils'], 'cwd': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'heting'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.17.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.17.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '17547'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '17849'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '79515'), ('NVM_DIR', '/home/<USER>/.nvm'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('COLCON_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'heting'), ('JOURNAL_STREAM', '8:18465921'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'heting'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/.nvm/versions/node/v22.17.0/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/heting-laptop:@/tmp/.ICE-unix/17826,unix/heting-laptop:/tmp/.ICE-unix/17826'), ('INVOCATION_ID', '09b55383b23b43179a1d1f2fa7eaf38d'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-f74425eeba7dedc8.txt'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4a42605598.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('NVM_CD_FLAGS', ''), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils:/opt/ros/humble')]), 'shell': False}
[0.046439] (nav2_test_utils) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.081076] (nav2_test_utils) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.085517] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.099790] (-) TimerEvent: {}
[0.133750] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.140427] (nav2_test_utils) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.140783] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.140958] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.143529] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.195487] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.199844] (-) TimerEvent: {}
[0.200297] (nav2_test_utils) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.200506] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.200842] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.202379] (nav2_test_utils) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.299942] (-) TimerEvent: {}
[0.304294] (nav2_test_utils) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.372177] (nav2_test_utils) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.397027] (nav2_test_utils) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.400032] (-) TimerEvent: {}
[0.400156] (nav2_test_utils) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.405059] (nav2_test_utils) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.412414] (nav2_test_utils) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.422952] (nav2_test_utils) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.451963] (nav2_test_utils) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.453418] (nav2_test_utils) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.500174] (-) TimerEvent: {}
[0.503731] (nav2_test_utils) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.524070] (nav2_test_utils) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.552452] (nav2_test_utils) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.557559] (nav2_test_utils) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[0.600272] (-) TimerEvent: {}
[0.603750] (nav2_test_utils) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[0.604059] (nav2_test_utils) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[0.660416] (nav2_test_utils) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[0.661454] (nav2_test_utils) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[0.693733] (nav2_test_utils) StdoutLine: {'line': b'-- Found rclcpp_action: 16.0.12 (/opt/ros/humble/share/rclcpp_action/cmake)\n'}
[0.700372] (-) TimerEvent: {}
[0.716736] (nav2_test_utils) StdoutLine: {'line': b'-- Found nav2_msgs: 1.1.18 (/opt/ros/humble/share/nav2_msgs/cmake)\n'}
[0.755452] (nav2_test_utils) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.800465] (-) TimerEvent: {}
[0.809282] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[0.816468] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[0.816764] (nav2_test_utils) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[0.816826] (nav2_test_utils) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[0.822391] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[0.822541] (nav2_test_utils) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[0.824920] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.831022] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[0.831150] (nav2_test_utils) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[0.832187] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.833436] (nav2_test_utils) StdoutLine: {'line': b'-- Configuring done\n'}
[0.846550] (nav2_test_utils) StdoutLine: {'line': b'-- Generating done\n'}
[0.854237] (nav2_test_utils) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils\n'}
[0.862840] (nav2_test_utils) CommandEnded: {'returncode': 0}
[0.863320] (nav2_test_utils) JobProgress: {'identifier': 'nav2_test_utils', 'progress': 'build'}
[0.863792] (nav2_test_utils) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'heting'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.17.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.17.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '17547'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '17849'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '79515'), ('NVM_DIR', '/home/<USER>/.nvm'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('COLCON_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'heting'), ('JOURNAL_STREAM', '8:18465921'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'heting'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/.nvm/versions/node/v22.17.0/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/heting-laptop:@/tmp/.ICE-unix/17826,unix/heting-laptop:/tmp/.ICE-unix/17826'), ('INVOCATION_ID', '09b55383b23b43179a1d1f2fa7eaf38d'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-f74425eeba7dedc8.txt'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4a42605598.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('NVM_CD_FLAGS', ''), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils:/opt/ros/humble')]), 'shell': False}
[0.895746] (nav2_test_utils) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/clicked_point_to_pose.dir/src/clicked_point_to_pose.cpp.o\x1b[0m\n'}
[0.895937] (nav2_test_utils) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/nav2_client_util.dir/src/nav2_client_util.cpp.o\x1b[0m\n'}
[0.900584] (-) TimerEvent: {}
[1.000893] (-) TimerEvent: {}
[1.101220] (-) TimerEvent: {}
[1.201561] (-) TimerEvent: {}
[1.301912] (-) TimerEvent: {}
[1.402242] (-) TimerEvent: {}
[1.502597] (-) TimerEvent: {}
[1.602947] (-) TimerEvent: {}
[1.703291] (-) TimerEvent: {}
[1.803647] (-) TimerEvent: {}
[1.903993] (-) TimerEvent: {}
[2.004262] (-) TimerEvent: {}
[2.104551] (-) TimerEvent: {}
[2.204827] (-) TimerEvent: {}
[2.305129] (-) TimerEvent: {}
[2.405431] (-) TimerEvent: {}
[2.505742] (-) TimerEvent: {}
[2.606063] (-) TimerEvent: {}
[2.706341] (-) TimerEvent: {}
[2.806627] (-) TimerEvent: {}
[2.906891] (-) TimerEvent: {}
[3.007189] (-) TimerEvent: {}
[3.107542] (-) TimerEvent: {}
[3.207863] (-) TimerEvent: {}
[3.308138] (-) TimerEvent: {}
[3.408438] (-) TimerEvent: {}
[3.508724] (-) TimerEvent: {}
[3.609023] (-) TimerEvent: {}
[3.709426] (-) TimerEvent: {}
[3.809738] (-) TimerEvent: {}
[3.910049] (-) TimerEvent: {}
[4.010354] (-) TimerEvent: {}
[4.110662] (-) TimerEvent: {}
[4.202866] (nav2_test_utils) StdoutLine: {'line': b'[ 75%] \x1b[32m\x1b[1mLinking CXX executable nav2_client_util\x1b[0m\n'}
[4.210757] (-) TimerEvent: {}
[4.311062] (-) TimerEvent: {}
[4.395501] (nav2_test_utils) StdoutLine: {'line': b'[ 75%] Built target nav2_client_util\n'}
[4.411189] (-) TimerEvent: {}
[4.511504] (-) TimerEvent: {}
[4.611802] (-) TimerEvent: {}
[4.712104] (-) TimerEvent: {}
[4.812411] (-) TimerEvent: {}
[4.912715] (-) TimerEvent: {}
[5.013000] (-) TimerEvent: {}
[5.113306] (-) TimerEvent: {}
[5.213604] (-) TimerEvent: {}
[5.313904] (-) TimerEvent: {}
[5.414179] (-) TimerEvent: {}
[5.514401] (-) TimerEvent: {}
[5.614671] (-) TimerEvent: {}
[5.714956] (-) TimerEvent: {}
[5.815241] (-) TimerEvent: {}
[5.915545] (-) TimerEvent: {}
[6.015832] (-) TimerEvent: {}
[6.116105] (-) TimerEvent: {}
[6.216397] (-) TimerEvent: {}
[6.316660] (-) TimerEvent: {}
[6.416941] (-) TimerEvent: {}
[6.517231] (-) TimerEvent: {}
[6.617493] (-) TimerEvent: {}
[6.717785] (-) TimerEvent: {}
[6.818067] (-) TimerEvent: {}
[6.918362] (-) TimerEvent: {}
[7.018626] (-) TimerEvent: {}
[7.118914] (-) TimerEvent: {}
[7.219215] (-) TimerEvent: {}
[7.319487] (-) TimerEvent: {}
[7.419778] (-) TimerEvent: {}
[7.520058] (-) TimerEvent: {}
[7.555127] (nav2_test_utils) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable clicked_point_to_pose\x1b[0m\n'}
[7.620159] (-) TimerEvent: {}
[7.720431] (-) TimerEvent: {}
[7.820720] (-) TimerEvent: {}
[7.888873] (nav2_test_utils) StdoutLine: {'line': b'[100%] Built target clicked_point_to_pose\n'}
[7.895952] (nav2_test_utils) CommandEnded: {'returncode': 0}
[7.896429] (nav2_test_utils) JobProgress: {'identifier': 'nav2_test_utils', 'progress': 'install'}
[7.901912] (nav2_test_utils) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'], 'cwd': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'heting'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.17.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.17.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '17547'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '17849'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '79515'), ('NVM_DIR', '/home/<USER>/.nvm'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('COLCON_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'heting'), ('JOURNAL_STREAM', '8:18465921'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'heting'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/.nvm/versions/node/v22.17.0/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/heting-laptop:@/tmp/.ICE-unix/17826,unix/heting-laptop:/tmp/.ICE-unix/17826'), ('INVOCATION_ID', '09b55383b23b43179a1d1f2fa7eaf38d'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-f74425eeba7dedc8.txt'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4a42605598.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('NVM_CD_FLAGS', ''), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils:/opt/ros/humble')]), 'shell': False}
[7.907135] (nav2_test_utils) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[7.907246] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util\n'}
[7.907755] (nav2_test_utils) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util" to ""\n'}
[7.907824] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose\n'}
[7.909791] (nav2_test_utils) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose" to ""\n'}
[7.909874] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/package_run_dependencies/nav2_test_utils\n'}
[7.909934] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/parent_prefix_path/nav2_test_utils\n'}
[7.910086] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.sh\n'}
[7.910160] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.dsv\n'}
[7.910249] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.sh\n'}
[7.910343] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.dsv\n'}
[7.910431] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.bash\n'}
[7.910508] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.sh\n'}
[7.910593] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.zsh\n'}
[7.910691] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.dsv\n'}
[7.910790] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv\n'}
[7.910889] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/packages/nav2_test_utils\n'}
[7.910983] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig.cmake\n'}
[7.911065] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig-version.cmake\n'}
[7.911162] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.xml\n'}
[7.912441] (nav2_test_utils) CommandEnded: {'returncode': 0}
[7.920980] (-) TimerEvent: {}
[7.925907] (nav2_test_utils) JobEnded: {'identifier': 'nav2_test_utils', 'rc': 0}
[7.926437] (-) EventReactorShutdown: {}
