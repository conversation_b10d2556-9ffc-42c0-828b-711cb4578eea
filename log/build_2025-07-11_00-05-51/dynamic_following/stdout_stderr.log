running egg_info
creating build/dynamic_following/dynamic_following.egg-info
writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
running build
running build_py
creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build
creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib
creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts
copying scripts/__init__.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts
copying scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts
creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following
copying dynamic_following/__init__.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following
running install
running install_lib
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts
copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts/__init__.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts
copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following
copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following/__init__.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following
byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/__init__.py to __init__.cpython-310.pyc
byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/test_behavior_trees.py to test_behavior_trees.cpython-310.pyc
byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following/__init__.py to __init__.cpython-310.pyc
running install_data
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index/packages
copying resource/dynamic_following -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index/packages
copying package.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
copying launch/tb3_simulation_following_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
copying launch/tb3_simulation_standard_bt_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
copying launch/tb3_simulation_simple_bt_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
copying launch/tb3_simulation_replanning_only_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
copying params/nav2_params.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
copying params/nav2_params_custom_bt.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
copying params/nav2_params_dynamic_following.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
copying params/nav2_multirobot_params_1.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
copying params/nav2_multirobot_params_2.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
copying params/nav2_multirobot_params_all.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
copying behavior_trees/follow_point.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
copying behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
copying behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
copying behavior_trees/navigate_to_pose_simple.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
copying behavior_trees/navigate_w_replanning_only.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps
copying maps/turtlebot3_world.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps
copying maps/turtlebot3_world.pgm -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz
copying rviz/nav2_default_view.rviz -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz
copying rviz/nav2_namespaced_view.rviz -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/urdf
copying urdf/turtlebot3_waffle.urdf -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/urdf
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds
copying worlds/waffle.model -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds
copying worlds/world_only.model -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds
creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts
copying scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts
copying README_BEHAVIOR_TREES.md -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following
running install_egg_info
Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
running install_scripts
Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
