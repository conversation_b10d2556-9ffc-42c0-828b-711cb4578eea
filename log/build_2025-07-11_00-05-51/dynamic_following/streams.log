[0.494s] Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[0.675s] running egg_info
[0.675s] creating build/dynamic_following/dynamic_following.egg-info
[0.676s] writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
[0.676s] writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
[0.676s] writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
[0.676s] writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
[0.676s] writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
[0.676s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.677s] reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.678s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[0.678s] running build
[0.678s] running build_py
[0.678s] creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build
[0.678s] creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib
[0.678s] creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts
[0.679s] copying scripts/__init__.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts
[0.679s] copying scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts
[0.679s] creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following
[0.679s] copying dynamic_following/__init__.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following
[0.679s] running install
[0.679s] running install_lib
[0.679s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts
[0.680s] copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts/__init__.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts
[0.680s] copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts
[0.680s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following
[0.680s] copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following/__init__.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following
[0.680s] byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/__init__.py to __init__.cpython-310.pyc
[0.680s] byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/test_behavior_trees.py to test_behavior_trees.cpython-310.pyc
[0.681s] byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following/__init__.py to __init__.cpython-310.pyc
[0.681s] running install_data
[0.681s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index
[0.682s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index
[0.682s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index/packages
[0.682s] copying resource/dynamic_following -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index/packages
[0.682s] copying package.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following
[0.682s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
[0.682s] copying launch/tb3_simulation_following_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
[0.682s] copying launch/tb3_simulation_standard_bt_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
[0.682s] copying launch/tb3_simulation_simple_bt_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
[0.682s] copying launch/tb3_simulation_replanning_only_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch
[0.682s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.682s] copying params/nav2_params.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.682s] copying params/nav2_params_custom_bt.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.683s] copying params/nav2_params_dynamic_following.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.683s] copying params/nav2_multirobot_params_1.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.683s] copying params/nav2_multirobot_params_2.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.683s] copying params/nav2_multirobot_params_all.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[0.683s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.683s] copying behavior_trees/follow_point.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.683s] copying behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.683s] copying behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.683s] copying behavior_trees/navigate_to_pose_simple.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.683s] copying behavior_trees/navigate_w_replanning_only.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees
[0.683s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps
[0.683s] copying maps/turtlebot3_world.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps
[0.684s] copying maps/turtlebot3_world.pgm -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps
[0.684s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz
[0.684s] copying rviz/nav2_default_view.rviz -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz
[0.684s] copying rviz/nav2_namespaced_view.rviz -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz
[0.684s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/urdf
[0.684s] copying urdf/turtlebot3_waffle.urdf -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/urdf
[0.684s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds
[0.684s] copying worlds/waffle.model -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds
[0.684s] copying worlds/world_only.model -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds
[0.684s] creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts
[0.684s] copying scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts
[0.684s] copying README_BEHAVIOR_TREES.md -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following
[0.684s] running install_egg_info
[0.685s] Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
[0.685s] running install_scripts
[0.702s] Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
[0.702s] writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
[0.721s] Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
