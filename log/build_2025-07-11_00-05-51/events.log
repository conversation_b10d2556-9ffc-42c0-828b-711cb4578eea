[0.000000] (-) TimerEvent: {}
[0.000175] (dynamic_following) JobQueued: {'identifier': 'dynamic_following', 'dependencies': OrderedDict()}
[0.000652] (dynamic_following) JobStarted: {'identifier': 'dynamic_following'}
[0.099808] (-) TimerEvent: {}
[0.200050] (-) TimerEvent: {}
[0.300292] (-) TimerEvent: {}
[0.400542] (-) TimerEvent: {}
[0.493093] (dynamic_following) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', 'build/dynamic_following', 'build', '--build-base', '/home/<USER>/projects/dynamic_following/build/dynamic_following/build', 'install', '--record', '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/projects/dynamic_following', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'heting', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'x11', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/projects', 'DESKTOP_SESSION': 'ubuntu-xorg', 'NVM_BIN': '/home/<USER>/.nvm/versions/node/v22.17.0/bin', 'NVM_INC': '/home/<USER>/.nvm/versions/node/v22.17.0/include/node', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'LC_MONETARY': 'zh_CN.UTF-8', 'SYSTEMD_EXEC_PID': '19700', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'NVM_DIR': '/home/<USER>/.nvm', 'MANDATORY_PATH': '/usr/share/gconf/ubuntu-xorg.mandatory.path', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'ibus', 'LOGNAME': 'heting', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'DEFAULTS_PATH': '/usr/share/gconf/ubuntu-xorg.default.path', 'USERNAME': 'heting', 'TERM': 'xterm-256color', 'GAZEBO_MODEL_PATH': ':/opt/ros/humble/share/turtlebot3_gazebo/models', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/.nvm/versions/node/v22.17.0/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/heting-laptop:@/tmp/.ICE-unix/17826,unix/heting-laptop:/tmp/.ICE-unix/17826', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/e26f5c36_af12_4149_9057_93e2d6589cf6', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu-xorg', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'GNOME_TERMINAL_SERVICE': ':1.113', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/humble', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu-xorg', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/projects/dynamic_following/build/dynamic_following', 'TURTLEBOT3_MODEL': 'waffle', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg', 'NVM_CD_FLAGS': '', 'XDG_DATA_DIRS': '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'VTE_VERSION': '6800'}, 'shell': False}
[0.500600] (-) TimerEvent: {}
[0.600808] (-) TimerEvent: {}
[0.675484] (dynamic_following) StdoutLine: {'line': b'running egg_info\n'}
[0.676030] (dynamic_following) StdoutLine: {'line': b'creating build/dynamic_following/dynamic_following.egg-info\n'}
[0.676165] (dynamic_following) StdoutLine: {'line': b'writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO\n'}
[0.676322] (dynamic_following) StdoutLine: {'line': b'writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt\n'}
[0.676423] (dynamic_following) StdoutLine: {'line': b'writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt\n'}
[0.676523] (dynamic_following) StdoutLine: {'line': b'writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt\n'}
[0.676623] (dynamic_following) StdoutLine: {'line': b'writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt\n'}
[0.676738] (dynamic_following) StdoutLine: {'line': b"writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'\n"}
[0.677927] (dynamic_following) StdoutLine: {'line': b"reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'\n"}
[0.678620] (dynamic_following) StdoutLine: {'line': b"writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'\n"}
[0.678744] (dynamic_following) StdoutLine: {'line': b'running build\n'}
[0.678823] (dynamic_following) StdoutLine: {'line': b'running build_py\n'}
[0.678935] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build\n'}
[0.679015] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib\n'}
[0.679090] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts\n'}
[0.679163] (dynamic_following) StdoutLine: {'line': b'copying scripts/__init__.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts\n'}
[0.679266] (dynamic_following) StdoutLine: {'line': b'copying scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts\n'}
[0.679345] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following\n'}
[0.679419] (dynamic_following) StdoutLine: {'line': b'copying dynamic_following/__init__.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following\n'}
[0.679511] (dynamic_following) StdoutLine: {'line': b'running install\n'}
[0.679595] (dynamic_following) StdoutLine: {'line': b'running install_lib\n'}
[0.680011] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts\n'}
[0.680129] (dynamic_following) StdoutLine: {'line': b'copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts/__init__.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts\n'}
[0.680204] (dynamic_following) StdoutLine: {'line': b'copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts\n'}
[0.680323] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following\n'}
[0.680421] (dynamic_following) StdoutLine: {'line': b'copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/dynamic_following/__init__.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following\n'}
[0.680599] (dynamic_following) StdoutLine: {'line': b'byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/__init__.py to __init__.cpython-310.pyc\n'}
[0.680748] (dynamic_following) StdoutLine: {'line': b'byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/test_behavior_trees.py to test_behavior_trees.cpython-310.pyc\n'}
[0.681805] (dynamic_following) StdoutLine: {'line': b'byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following/__init__.py to __init__.cpython-310.pyc\n'}
[0.681920] (dynamic_following) StdoutLine: {'line': b'running install_data\n'}
[0.682029] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index\n'}
[0.682123] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index\n'}
[0.682201] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index/packages\n'}
[0.682277] (dynamic_following) StdoutLine: {'line': b'copying resource/dynamic_following -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/ament_index/resource_index/packages\n'}
[0.682397] (dynamic_following) StdoutLine: {'line': b'copying package.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following\n'}
[0.682475] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch\n'}
[0.682553] (dynamic_following) StdoutLine: {'line': b'copying launch/tb3_simulation_following_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch\n'}
[0.682629] (dynamic_following) StdoutLine: {'line': b'copying launch/tb3_simulation_standard_bt_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch\n'}
[0.682701] (dynamic_following) StdoutLine: {'line': b'copying launch/tb3_simulation_simple_bt_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch\n'}
[0.682828] (dynamic_following) StdoutLine: {'line': b'copying launch/tb3_simulation_replanning_only_launch.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/launch\n'}
[0.682923] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params\n'}
[0.683013] (dynamic_following) StdoutLine: {'line': b'copying params/nav2_params.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params\n'}
[0.683089] (dynamic_following) StdoutLine: {'line': b'copying params/nav2_params_custom_bt.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params\n'}
[0.683165] (dynamic_following) StdoutLine: {'line': b'copying params/nav2_params_dynamic_following.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params\n'}
[0.683240] (dynamic_following) StdoutLine: {'line': b'copying params/nav2_multirobot_params_1.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params\n'}
[0.683315] (dynamic_following) StdoutLine: {'line': b'copying params/nav2_multirobot_params_2.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params\n'}
[0.683389] (dynamic_following) StdoutLine: {'line': b'copying params/nav2_multirobot_params_all.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params\n'}
[0.683462] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees\n'}
[0.683537] (dynamic_following) StdoutLine: {'line': b'copying behavior_trees/follow_point.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees\n'}
[0.683610] (dynamic_following) StdoutLine: {'line': b'copying behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees\n'}
[0.683706] (dynamic_following) StdoutLine: {'line': b'copying behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees\n'}
[0.683842] (dynamic_following) StdoutLine: {'line': b'copying behavior_trees/navigate_to_pose_simple.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees\n'}
[0.683938] (dynamic_following) StdoutLine: {'line': b'copying behavior_trees/navigate_w_replanning_only.xml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/behavior_trees\n'}
[0.684011] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps\n'}
[0.684081] (dynamic_following) StdoutLine: {'line': b'copying maps/turtlebot3_world.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps\n'}
[0.684151] (dynamic_following) StdoutLine: {'line': b'copying maps/turtlebot3_world.pgm -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/maps\n'}
[0.684222] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz\n'}
[0.684292] (dynamic_following) StdoutLine: {'line': b'copying rviz/nav2_default_view.rviz -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz\n'}
[0.684362] (dynamic_following) StdoutLine: {'line': b'copying rviz/nav2_namespaced_view.rviz -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz\n'}
[0.684436] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/urdf\n'}
[0.684508] (dynamic_following) StdoutLine: {'line': b'copying urdf/turtlebot3_waffle.urdf -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/urdf\n'}
[0.684581] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds\n'}
[0.684650] (dynamic_following) StdoutLine: {'line': b'copying worlds/waffle.model -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds\n'}
[0.684720] (dynamic_following) StdoutLine: {'line': b'copying worlds/world_only.model -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/worlds\n'}
[0.684790] (dynamic_following) StdoutLine: {'line': b'creating /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts\n'}
[0.684869] (dynamic_following) StdoutLine: {'line': b'copying scripts/test_behavior_trees.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts\n'}
[0.684941] (dynamic_following) StdoutLine: {'line': b'copying README_BEHAVIOR_TREES.md -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following\n'}
[0.685013] (dynamic_following) StdoutLine: {'line': b'running install_egg_info\n'}
[0.685254] (dynamic_following) StdoutLine: {'line': b'Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info\n'}
[0.685764] (dynamic_following) StdoutLine: {'line': b'running install_scripts\n'}
[0.700886] (-) TimerEvent: {}
[0.702476] (dynamic_following) StdoutLine: {'line': b'Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following\n'}
[0.702757] (dynamic_following) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'\n"}
[0.721068] (dynamic_following) CommandEnded: {'returncode': 0}
[0.727728] (dynamic_following) JobEnded: {'identifier': 'dynamic_following', 'rc': 0}
[0.728147] (-) EventReactorShutdown: {}
