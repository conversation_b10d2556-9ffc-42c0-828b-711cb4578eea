[0.879s] Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[1.192s] running egg_info
[1.192s] writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
[1.193s] writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
[1.193s] writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
[1.193s] writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
[1.193s] writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
[1.196s] reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[1.197s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[1.197s] running build
[1.197s] running build_py
[1.197s] copying scripts/test_goal_updater.py -> /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts
[1.198s] running install
[1.198s] running install_lib
[1.199s] copying /home/<USER>/projects/dynamic_following/build/dynamic_following/build/lib/scripts/test_goal_updater.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts
[1.200s] byte-compiling /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/scripts/test_goal_updater.py to test_goal_updater.cpython-310.pyc
[1.202s] running install_data
[1.203s] copying scripts/test_goal_updater.py -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/scripts
[1.203s] running install_egg_info
[1.208s] removing '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info' (and everything under it)
[1.208s] Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
[1.209s] running install_scripts
[1.237s] Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
[1.238s] Installing test_goal_updater script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
[1.239s] writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
[1.279s] Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
