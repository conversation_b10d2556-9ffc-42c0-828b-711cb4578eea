[0.857s] Invoking command in '/home/<USER>/projects/dynamic_following': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
[1.161s] running egg_info
[1.163s] writing build/dynamic_following/dynamic_following.egg-info/PKG-INFO
[1.163s] writing dependency_links to build/dynamic_following/dynamic_following.egg-info/dependency_links.txt
[1.163s] writing entry points to build/dynamic_following/dynamic_following.egg-info/entry_points.txt
[1.163s] writing requirements to build/dynamic_following/dynamic_following.egg-info/requires.txt
[1.163s] writing top-level names to build/dynamic_following/dynamic_following.egg-info/top_level.txt
[1.167s] reading manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[1.169s] writing manifest file 'build/dynamic_following/dynamic_following.egg-info/SOURCES.txt'
[1.169s] running build
[1.169s] running build_py
[1.170s] running install
[1.170s] running install_lib
[1.173s] running install_data
[1.173s] copying params/nav2_params_custom_bt.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[1.173s] copying params/nav2_params_dynamic_following.yaml -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/params
[1.174s] copying rviz/nav2_default_view.rviz -> /home/<USER>/projects/dynamic_following/install/dynamic_following/share/dynamic_following/rviz
[1.174s] running install_egg_info
[1.177s] removing '/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info' (and everything under it)
[1.177s] Copying build/dynamic_following/dynamic_following.egg-info to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages/dynamic_following-0.0.0-py3.10.egg-info
[1.178s] running install_scripts
[1.208s] Installing test_behavior_trees script to /home/<USER>/projects/dynamic_following/install/dynamic_following/lib/dynamic_following
[1.209s] writing list of installed files to '/home/<USER>/projects/dynamic_following/build/dynamic_following/install.log'
[1.252s] Invoked command in '/home/<USER>/projects/dynamic_following' returned '0': PYTHONPATH=/home/<USER>/projects/dynamic_following/build/dynamic_following/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/projects/dynamic_following/install/dynamic_following/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base build/dynamic_following build --build-base /home/<USER>/projects/dynamic_following/build/dynamic_following/build install --record /home/<USER>/projects/dynamic_following/build/dynamic_following/install.log --single-version-externally-managed install_data
