#!/usr/bin/env python3

# Copyright (c) 2018 Intel Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Launch script for TurtleBot3 simulation with standard Nav2 behavior trees."""

import os

from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource


def generate_launch_description():
    # Get the current package directory
    pkg_dir = get_package_share_directory("dynamic_following")
    
    # Include the main launch file with standard behavior tree parameters
    main_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_dir, "launch", "tb3_simulation_following_launch.py")
        ),
        launch_arguments={
            "params_file": os.path.join(pkg_dir, "params", "nav2_params_custom_bt.yaml"),
            "behavior_tree_mode": "standard",
            "default_nav_to_pose_bt_xml": os.path.join(
                pkg_dir, "behavior_trees", "navigate_to_pose_w_replanning_and_recovery.xml"
            ),
            "default_nav_through_poses_bt_xml": os.path.join(
                pkg_dir, "behavior_trees", "navigate_through_poses_w_replanning_and_recovery.xml"
            ),
        }.items(),
    )

    # Create the launch description and populate
    ld = LaunchDescription()
    ld.add_action(main_launch)

    return ld
