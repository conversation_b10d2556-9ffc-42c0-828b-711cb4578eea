#!/usr/bin/env python3

"""
Test script for validating custom behavior tree integration.
This script performs basic checks to ensure behavior trees are properly configured.
"""

import os
import sys
import time
import rclpy
from rclpy.node import Node
from rclpy.parameter import Parameter
from geometry_msgs.msg import PoseStamped
from nav2_msgs.action import NavigateToPose
from rclpy.action import ActionClient
from ament_index_python.packages import get_package_share_directory


class BehaviorTreeTester(Node):
    def __init__(self):
        super().__init__('behavior_tree_tester')
        
        # Action client for navigation
        self.nav_client = ActionClient(self, NavigateToPose, 'navigate_to_pose')
        
        # Publisher for goal poses
        self.goal_pub = self.create_publisher(PoseStamped, 'goal_pose', 10)
        
        self.get_logger().info('Behavior Tree Tester initialized')

    def check_bt_navigator_params(self):
        """Check if bt_navigator has the correct parameters set."""
        self.get_logger().info('Checking bt_navigator parameters...')
        
        try:
            # Try to get bt_navigator parameters
            bt_nav_node = self.create_client(
                rclpy.parameter.GetParameters, 
                '/bt_navigator/get_parameters'
            )
            
            if bt_nav_node.wait_for_service(timeout_sec=5.0):
                self.get_logger().info('✓ bt_navigator service is available')
                return True
            else:
                self.get_logger().error('✗ bt_navigator service not available')
                return False
                
        except Exception as e:
            self.get_logger().error(f'✗ Error checking bt_navigator: {e}')
            return False

    def check_behavior_tree_files(self):
        """Check if behavior tree XML files exist and are readable."""
        self.get_logger().info('Checking behavior tree XML files...')
        
        try:
            pkg_dir = get_package_share_directory('dynamic_following')
            bt_dir = os.path.join(pkg_dir, 'behavior_trees')
            
            expected_files = [
                'follow_point.xml',
                'navigate_to_pose_w_replanning_and_recovery.xml',
                'navigate_through_poses_w_replanning_and_recovery.xml',
                'navigate_to_pose_simple.xml',
                'navigate_w_replanning_only.xml'
            ]
            
            all_exist = True
            for bt_file in expected_files:
                file_path = os.path.join(bt_dir, bt_file)
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    self.get_logger().info(f'✓ Found: {bt_file}')
                    
                    # Check if file is readable and has content
                    try:
                        with open(file_path, 'r') as f:
                            content = f.read()
                            if '<root BTCPP_format="4"' in content:
                                self.get_logger().info(f'✓ Valid BT format: {bt_file}')
                            else:
                                self.get_logger().warning(f'⚠ Questionable BT format: {bt_file}')
                    except Exception as e:
                        self.get_logger().error(f'✗ Cannot read {bt_file}: {e}')
                        all_exist = False
                else:
                    self.get_logger().error(f'✗ Missing: {bt_file}')
                    all_exist = False
            
            return all_exist
            
        except Exception as e:
            self.get_logger().error(f'✗ Error checking behavior tree files: {e}')
            return False

    def check_navigation_action_server(self):
        """Check if navigation action server is available."""
        self.get_logger().info('Checking navigation action server...')
        
        if self.nav_client.wait_for_server(timeout_sec=10.0):
            self.get_logger().info('✓ Navigation action server is available')
            return True
        else:
            self.get_logger().error('✗ Navigation action server not available')
            return False

    def send_test_goal(self):
        """Send a test navigation goal."""
        self.get_logger().info('Sending test navigation goal...')
        
        goal_msg = NavigateToPose.Goal()
        goal_msg.pose.header.frame_id = 'map'
        goal_msg.pose.header.stamp = self.get_clock().now().to_msg()
        goal_msg.pose.pose.position.x = 2.0
        goal_msg.pose.pose.position.y = 1.0
        goal_msg.pose.pose.position.z = 0.0
        goal_msg.pose.pose.orientation.w = 1.0
        
        try:
            future = self.nav_client.send_goal_async(goal_msg)
            self.get_logger().info('✓ Test goal sent successfully')
            return True
        except Exception as e:
            self.get_logger().error(f'✗ Failed to send test goal: {e}')
            return False

    def run_tests(self):
        """Run all tests."""
        self.get_logger().info('Starting behavior tree integration tests...')
        
        tests = [
            ('Behavior Tree Files', self.check_behavior_tree_files),
            ('bt_navigator Parameters', self.check_bt_navigator_params),
            ('Navigation Action Server', self.check_navigation_action_server),
            ('Test Goal Sending', self.send_test_goal),
        ]
        
        results = {}
        for test_name, test_func in tests:
            self.get_logger().info(f'\n--- Running: {test_name} ---')
            results[test_name] = test_func()
            time.sleep(1)  # Brief pause between tests
        
        # Summary
        self.get_logger().info('\n=== TEST SUMMARY ===')
        passed = 0
        total = len(tests)
        
        for test_name, result in results.items():
            status = '✓ PASS' if result else '✗ FAIL'
            self.get_logger().info(f'{test_name}: {status}')
            if result:
                passed += 1
        
        self.get_logger().info(f'\nTests passed: {passed}/{total}')
        
        if passed == total:
            self.get_logger().info('🎉 All tests passed! Behavior tree integration is working correctly.')
            return True
        else:
            self.get_logger().error('❌ Some tests failed. Please check the configuration.')
            return False


def main():
    rclpy.init()
    
    tester = BehaviorTreeTester()
    
    try:
        success = tester.run_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        tester.get_logger().info('Test interrupted by user')
    except Exception as e:
        tester.get_logger().error(f'Test failed with exception: {e}')
        sys.exit(1)
    finally:
        tester.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
