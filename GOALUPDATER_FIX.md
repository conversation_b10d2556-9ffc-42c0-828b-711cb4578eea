# GoalUpdater Fix Documentation

## Problem
The GoalUpdater behavior tree node was not subscribing to the `/goal_update` topic, causing <PERSON><PERSON><PERSON>'s "Publish Point" feature to be ineffective for dynamic goal updates.

## Root Cause
The Nav2 GoalUpdater node requires explicit configuration of the `goal_updater_topic` parameter in the bt_navigator configuration. Without this parameter, the GoalUpdater node uses its default topic name but doesn't properly subscribe to it.

## Solution
Added the following parameters to the bt_navigator configuration in the Nav2 parameter files:

```yaml
bt_navigator:
  ros__parameters:
    # ... other parameters ...
    # GoalUpdater node configuration
    goal_updater_topic: "goal_update"
    goals_updater_topic: "goals_update"
    # ... rest of configuration ...
```

## Files Modified
1. `params/nav2_params_dynamic_following.yaml` - Added GoalUpdater topic configuration
2. `params/nav2_params_custom_bt.yaml` - Added GoalUpdater topic configuration
3. `scripts/test_goal_updater.py` - Created test script to verify GoalUpdater subscription
4. `setup.py` - Added test script to console scripts

## How to Test the Fix

### Method 1: Using the Test Script
1. Build the package:
   ```bash
   colcon build --packages-select dynamic_following
   source install/setup.bash
   ```

2. Launch Nav2 with dynamic following:
   ```bash
   ros2 launch dynamic_following tb3_simulation_following_launch.py
   ```

3. In another terminal, run the test script:
   ```bash
   ros2 run dynamic_following test_goal_updater
   ```

4. The test script should show:
   ```
   ✓ Found 1 subscriber(s) to /goal_update:
     - Node: bt_navigator, Namespace: /
   ```

### Method 2: Manual Testing with RViz
1. Launch the simulation:
   ```bash
   ros2 launch dynamic_following tb3_simulation_following_launch.py
   ```

2. In RViz, use the "Publish Point" tool to click on different locations on the map

3. The robot should dynamically update its goal and navigate to the clicked points

### Method 3: Command Line Testing
1. Launch the simulation

2. Check topic subscribers:
   ```bash
   ros2 topic info /goal_update
   ```

3. Manually publish a goal:
   ```bash
   ros2 topic pub /goal_update geometry_msgs/PoseStamped "
   header:
     frame_id: 'map'
   pose:
     position: {x: 2.0, y: 1.0, z: 0.0}
     orientation: {w: 1.0}"
   ```

## Expected Behavior
- The GoalUpdater node should now properly subscribe to `/goal_update`
- RViz "Publish Point" should work for dynamic goal updates
- The robot should navigate to clicked points in real-time
- The behavior tree should use updated goals from the topic

## Technical Details
The GoalUpdater is a decorator node in Nav2's behavior tree system that:
1. Subscribes to a configurable topic (default: "goal_update")
2. Updates the goal pose for its child nodes when new goals are received
3. Passes through the original goal if no updates are received

The fix ensures that the topic subscription is properly configured and active.
