#!/usr/bin/env python3

"""
Quick verification script to check if GoalUpdater fix is working.
This script checks the Nav2 parameter configuration for GoalUpdater topic settings.
"""

import yaml
import os

def check_goalupdater_config():
    """Check if GoalUpdater configuration is present in Nav2 params."""
    try:
        # Check the installed configuration file
        params_file = 'install/dynamic_following/share/dynamic_following/params/nav2_params_dynamic_following.yaml'
        
        print(f"Checking configuration file: {params_file}")
        
        # Read the YAML file
        with open(params_file, 'r') as file:
            config = yaml.safe_load(file)
        
        # Check bt_navigator configuration
        if 'bt_navigator' in config and 'ros__parameters' in config['bt_navigator']:
            bt_params = config['bt_navigator']['ros__parameters']
            
            # Check for GoalUpdater topic configuration
            goal_updater_topic = bt_params.get('goal_updater_topic')
            goals_updater_topic = bt_params.get('goals_updater_topic')
            
            print("\n=== GoalUpdater Configuration Check ===")
            
            if goal_updater_topic:
                print(f"✓ goal_updater_topic: {goal_updater_topic}")
            else:
                print("✗ goal_updater_topic: NOT CONFIGURED")
                
            if goals_updater_topic:
                print(f"✓ goals_updater_topic: {goals_updater_topic}")
            else:
                print("✗ goals_updater_topic: NOT CONFIGURED")
            
            # Check if nav2_goal_updater_node_bt_node is in plugin list
            plugin_libs = bt_params.get('plugin_lib_names', [])
            if 'nav2_goal_updater_node_bt_node' in plugin_libs:
                print("✓ nav2_goal_updater_node_bt_node plugin: LOADED")
            else:
                print("✗ nav2_goal_updater_node_bt_node plugin: NOT LOADED")
            
            # Overall status
            if goal_updater_topic and goals_updater_topic and 'nav2_goal_updater_node_bt_node' in plugin_libs:
                print("\n🎉 GoalUpdater fix is PROPERLY CONFIGURED!")
                print("The GoalUpdater should now subscribe to /goal_update topic.")
                return True
            else:
                print("\n❌ GoalUpdater fix is INCOMPLETE!")
                print("Some configuration is missing.")
                return False
                
        else:
            print("❌ bt_navigator configuration not found!")
            return False
            
    except FileNotFoundError:
        print(f"❌ Configuration file not found: {params_file}")
        return False
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False

def main():
    print("GoalUpdater Fix Verification")
    print("=" * 40)
    
    success = check_goalupdater_config()
    
    if success:
        print("\n=== How to Test the Fix ===")
        print("1. Launch the simulation:")
        print("   ros2 launch dynamic_following tb3_simulation_following_launch.py")
        print()
        print("2. Check if GoalUpdater is subscribing to /goal_update:")
        print("   ros2 topic info /goal_update")
        print()
        print("3. Test with RViz 'Publish Point' tool")
    else:
        print("\n❌ Fix verification failed!")

if __name__ == '__main__':
    main()
