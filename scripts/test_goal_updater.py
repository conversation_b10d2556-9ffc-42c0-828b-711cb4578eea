#!/usr/bin/env python3

"""
Test script to verify GoalUpdater subscription to /goal_update topic.
This script checks if the GoalUpdater node is properly configured and subscribing to the goal_update topic.
"""

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped
import time


class GoalUpdaterTester(Node):
    def __init__(self):
        super().__init__('goal_updater_tester')
        
        # Publisher for goal updates
        self.goal_pub = self.create_publisher(PoseStamped, 'goal_update', 10)
        
        # Timer to publish test goals
        self.timer = self.create_timer(2.0, self.publish_test_goal)
        
        self.goal_count = 0
        self.get_logger().info('GoalUpdater Tester initialized')
        self.get_logger().info('Publishing test goals to /goal_update topic every 2 seconds')

    def publish_test_goal(self):
        """Publish a test goal to the goal_update topic."""
        goal_msg = PoseStamped()
        goal_msg.header.frame_id = 'map'
        goal_msg.header.stamp = self.get_clock().now().to_msg()
        
        # Create different goal positions for testing
        positions = [
            (2.0, 1.0),
            (1.0, 2.0),
            (-1.0, 1.0),
            (0.0, -1.0)
        ]
        
        pos_x, pos_y = positions[self.goal_count % len(positions)]
        goal_msg.pose.position.x = pos_x
        goal_msg.pose.position.y = pos_y
        goal_msg.pose.position.z = 0.0
        goal_msg.pose.orientation.w = 1.0
        
        self.goal_pub.publish(goal_msg)
        self.goal_count += 1
        
        self.get_logger().info(f'Published goal #{self.goal_count}: ({pos_x}, {pos_y})')

    def check_subscribers(self):
        """Check if there are any subscribers to the goal_update topic."""
        topic_info = self.get_subscriptions_info_by_topic('goal_update')
        if topic_info:
            self.get_logger().info(f'✓ Found {len(topic_info)} subscriber(s) to /goal_update:')
            for info in topic_info:
                self.get_logger().info(f'  - Node: {info.node_name}, Namespace: {info.node_namespace}')
            return True
        else:
            self.get_logger().warn('✗ No subscribers found for /goal_update topic')
            return False


def main():
    rclpy.init()
    
    tester = GoalUpdaterTester()
    
    # Wait a moment for discovery
    time.sleep(2.0)
    
    # Check for subscribers
    has_subscribers = tester.check_subscribers()
    
    if has_subscribers:
        tester.get_logger().info('GoalUpdater appears to be working! Starting test goal publishing...')
        try:
            rclpy.spin(tester)
        except KeyboardInterrupt:
            tester.get_logger().info('Test interrupted by user')
    else:
        tester.get_logger().error('GoalUpdater is not subscribing to /goal_update. Check your Nav2 configuration.')
    
    tester.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()
